package com.iqiyi.vip.domain.task;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.iqiyi.vip.domain.rights.service.RightsService;
import com.iqiyi.vip.dto.base.BaseResponse;
import com.iqiyi.vip.dto.rights.ReceiveRightsReq;
import com.iqiyi.vip.dto.rights.PartnerRightsResponse;
import com.iqiyi.vip.enums.TaskTypeEnum;
import com.iqiyi.vip.utils.ApplicationContextUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

/**
 * 开通权益重试任务
 *
 * <AUTHOR>
 * @date 2023/12/20 10:13
 */
@Data
@Slf4j
@NoArgsConstructor
@AllArgsConstructor
public class OpenRightsRetryTask extends AbstractTask {

    private ReceiveRightsReq receiveRightsReq;

    @Override
    protected boolean execute() {
        try {
            log.info("[start][receiveRightsReq:{}]", receiveRightsReq);
            RightsService rightsService = (RightsService) ApplicationContextUtil.getBean("rightsService");
            BaseResponse<PartnerRightsResponse> response = rightsService.receiveRights(receiveRightsReq);
            boolean success = response != null && response.suc();
            log.info("[end][receiveRightsReq:{},success:{}]", receiveRightsReq, success);
            return success;
        } catch (Exception e) {
            log.error("OpenRightsRetryTask execute failed, receiveRightsReq:{}", receiveRightsReq, e);
            return false;
        }
    }

    @SneakyThrows
    @Override
    public void deserialize(String data) throws IllegalArgumentException {
        ObjectMapper objectMapper = new ObjectMapper();
        receiveRightsReq = objectMapper.readValue(data, ReceiveRightsReq.class);
    }

    @SneakyThrows
    @Override
    public String serialize() {
        ObjectMapper objectMapper = new ObjectMapper();
        return objectMapper.writeValueAsString(receiveRightsReq);
    }

    @Override
    public int getDefaultPoolType() {
        return TaskTypeEnum.OPEN_RIGHTS_RETRY.getType();
    }
}
