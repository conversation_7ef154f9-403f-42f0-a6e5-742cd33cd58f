package com.iqiyi.vip.enums;

import lombok.Getter;

/**
 * 异步任务类型枚举
 *
 * <AUTHOR>
 * @date 2022/06/20
 */
@Getter
public enum TaskTypeEnum {

    SEND_ACTIVITY_MSG(1, "活动创建消息"),
    SAVE_FULFILLMENT_CONFIG(101, "保存履约条件任务"),
    FULFILL_ORDER(102, "履约重试"),
    PAID_ORDER_CALL_BACK(103, "正单回调交易"),
    RIGHTS_EXACT_MSG(104, "给精准触达发权益领取提醒"),
    VIP_TAG(105, "更新标签信息"),
    TERMINATE_ORDER(106, "解约重试"),
    REFUND_ORDER_CALL_BACK(107, "退单回调交易"),
    DEDUCT_STOCK(108, "解约重试"),
    RIGHTS_OPENED_MSG(109, "权益开通"),
    RIGHTS_RECYCLE_MSG(110, "权益回收"),
    VIP_FULFILL_ORDER(111, "VIP履约重试"),
    VIP_CANCEL_ORDER(112, "取消会员订单"),
    OPEN_RIGHTS_RETRY(113, "开通权益重试"),;

    private Integer type;
    private String desc;

    TaskTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

}
